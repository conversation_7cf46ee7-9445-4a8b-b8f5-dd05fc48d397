# 开发规范和规则

- 用户要求在开始实施前必须深入了解所有官方文档，确定具体的实现方案、前端设计要求、后端接入细节和功能优先级，不能匆忙开始编码
- 用户明确要求在开始任何实施前必须获得明确许可，不能自行开始编码。用户强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户要求测试完API连接后，记住要配置环境变量以提高安全性，将硬编码的API密钥移到环境变量中
- 不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行，用户自己处理这些
- 用户严重质疑AI的分析，要求不允许乱分析乱改，强调不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行
- 用户对AI的修改非常愤怒，指出协作计划的支出不会统计到首页，数据流被反复更改导致问题，要求立即修复
- 协作支出统计修复：首页财务概览必须显示本月数据，协作支出查询需要包含用户创建的和参与的所有协作计划，数据去重使用Map按_id去重，缓存清理需要通知所有协作者
